import { API_CONFIG, API_ENDPOINTS, buildApiUrl } from '@/config/api';

// Types for API responses
export interface ApiResponse<T> {
  success: boolean;
  data: T | null;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  message?: string;
}


// Common request options for DELETE/GET with query params
export type RequestOptions = { params?: Record<string, string> };

// User DTOs for admin operations
export interface UserDto {
  id: string;
  email: string;
  name: string;
  role: string;
  avatarUrl?: string;
  isActive?: boolean;
  lastLogin?: string;
  createdAt?: string;
  organization?: {
    id: string;
    name: string;
    domain: string;
    industry: string;
    employeeCount: number;
  };
  employeeDetails?: EmployeeDetailsDto;
  tempPassword?: string; // Only for newly created employees

  // Login credential fields for automatic credential generation
  loginUsername?: string; // Generated login username/email
  loginTemporaryPassword?: string; // Generated temporary password (plain text, only for response)
  requirePasswordReset?: boolean; // Flag for first-time login password reset
  passwordGeneratedAt?: string; // When the credentials were generated
}

export interface EmployeeDetailsDto {
  employeeId: string;
  jobTitle: string;
  department: string;
  manager?: string;
  joinDate: string;
  employmentType: string;
  employmentStatus: string;
  workLocation?: string;
  phone?: string;
  address?: string;
  dateOfBirth?: string;
  baseSalary?: number;
  annualCTC?: number;
  nextSalaryReview?: string;
  performanceRating?: number;
  totalExperience?: string;

  // Personal information
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelation?: string;
  bloodGroup?: string;
  maritalStatus?: string;
  gender?: string;
  nationality?: string;

  // Banking information
  bankAccountNumber?: string;
  bankIFSC?: string;
  bankName?: string;

  // Government IDs
  pan?: string;
  aadhar?: string;

  // Employment dates
  probationEndDate?: string;
  confirmationDate?: string;
  resignationDate?: string;
  lastWorkingDate?: string;
  resignationReason?: string;

  // Login credential fields for automatic credential generation
  loginUsername?: string; // Generated login username/email
  loginTemporaryPassword?: string; // Generated temporary password (only visible to admins)
  requirePasswordReset?: boolean; // Flag for first-time login password reset
  passwordGeneratedAt?: string; // When the credentials were generated
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
}

export interface GetUsersParams {
  page?: number;
  limit?: number;
  department?: string;
  role?: string;
  search?: string;
}

export interface CreateEmployeeRequest {
  name: string;
  email: string;
  role: string; // 'employee' | 'orgadmin' etc.
  employeeDetails: {
    employeeId: string;
    jobTitle: string;
    department: string;
    managerId?: string;
    joinDate: string; // ISO
    employmentType: string; // 'FullTime' | 'PartTime' | 'Contract' | 'Internship'
    workLocation?: string;
    phone?: string;
    address?: string;
    dateOfBirth?: string; // ISO date string
    baseSalary?: number;
    annualCTC?: number;
    // Emergency contact information
    emergencyContactName?: string;
    emergencyContactPhone?: string;
    emergencyContactRelation?: string;
    // Additional personal information
    bloodGroup?: string;
    maritalStatus?: string;
    gender?: string;
    nationality?: string;
    // Banking information
    bankAccountNumber?: string;
    bankIFSC?: string;
    bankName?: string;
    // Government IDs
    pan?: string;
    aadhar?: string;
  };
}

export interface UpdateUserRequest {
  name?: string;
  email?: string;
  role?: string;
  isActive?: boolean;
  employeeDetails?: {
    jobTitle?: string;
    department?: string;
    managerId?: string;
    employmentType?: string;
    employmentStatus?: string;
    workLocation?: string;
    phone?: string;
    address?: string;
    dateOfBirth?: string;
    baseSalary?: number;
    annualCTC?: number;
    // Emergency contact information
    emergencyContactName?: string;
    emergencyContactPhone?: string;
    emergencyContactRelation?: string;
    // Additional personal information
    bloodGroup?: string;
    maritalStatus?: string;
    gender?: string;
    nationality?: string;
    // Banking information
    bankAccountNumber?: string;
    bankIFSC?: string;
    bankName?: string;
    // Government IDs
    pan?: string;
    aadhar?: string;
  };
}

// Types for authentication
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: {
    id: string;
    email: string;
    name: string;
    role: string;
    avatarUrl?: string;
    organization?: {
      id: string;
      name: string;
      domain: string;
      industry: string;
      employeeCount: number;
    };
    employeeDetails?: {
      employeeId?: string;
      jobTitle?: string;
      department?: string;
      manager?: string;
      joinDate?: string;
      employmentType?: string;
      employmentStatus?: string;
      workLocation?: string;
      phone?: string;
      address?: string;
      dateOfBirth?: string;
      baseSalary?: string;
      annualCTC?: string;
      nextSalaryReview?: string;
      education?: Array<{
        degree: string;
        institution: string;
        year: string;
      }>;
      skills?: Array<{
        name: string;
        level: number;
      }>;
      performanceRating?: number;
      totalExperience?: string;
    };
  };
  expiresIn: number;
}

export interface OrganizationRegistrationRequest {
  organization: {
    name: string;
    domain: string;
    industry: string;
    employeeCount: number;
    subscriptionPlan: string;
  };
  admin: {
    name: string;
    email: string;
    password: string;
  };
}

export interface OrganizationResponse {
  id: string;
  name: string;
  domain: string;
  industry: string;
  employeeCount: number;
}

// Dashboard interfaces
export interface SuperAdminDashboard {
  metrics: {
    totalOrganizations: number;
    activeOrganizations: number;
    totalUsers: number;
    monthlyRevenue: number;
    systemUptime: number;
    pendingApprovals: number;
    systemAlerts: number;
    revenueGrowth: number;
    userGrowth: number;
  };
  recentActivities: Array<{
    id: string;
    type: string;
    message: string;
    timestamp: string;
    status: string;
    priority: string;
  }>;
  systemHealth: Array<{
    name: string;
    value: string;
    status: string;
    trend: string;
  }>;
  topOrganizations: Array<{
    id: string;
    name: string;
    domain: string;
    industry: string;
    employeeCount: number;
    status: string;
    monthlyRevenue: number;
    joinDate: string;
  }>;
}

export interface OrganizationAdminDashboard {
  metrics: {
    totalEmployees: number;
    activeEmployees: number;
    newHiresThisMonth: number;
    pendingLeaveRequests: number;
    attendanceRate: number;
    averagePerformanceRating: number;
  };
  recentActivities: Array<{
    type: string;
    message: string;
    timestamp: string;
  }>;
  upcomingReviews: Array<{
    employeeName: string;
    reviewDate: string;
    reviewType: string;
  }>;
}

export interface EmployeeDashboard {
  metrics: {
    totalLeaves: number;
    usedLeaves: number;
    remainingLeaves: number;
    pendingLeaveRequests: number;
    totalLeaveRequests: number; // Added for leave requests count
    todayHours: number;
    weekHours: number;
    monthHours: number;
    activeTasks: number;
    completedTasks: number;
    performanceScore: number;
    nextReviewDate: string | null;
  };
  recentActivities: Array<{
    id: string;
    type: string;
    message: string;
    timestamp: string;
    status: string;
  }>;
  profileSummary: {
    name: string;
    jobTitle: string;
    department: string;
    employeeId: string;
    joinDate: string;
    manager: string;
    workLocation: string;
  };
  upcomingTasks: Array<{
    id: string;
    title: string;
    description: string;
    dueDate: string;
    priority: string;
    status: string;
  }>;
  attendanceSummary: {
    isCheckedIn: boolean;
    checkInTime: string | null;
    checkOutTime: string | null;
    todayWorkHours: number;
    weekWorkHours: number;
    monthWorkHours: number;
    attendanceRate: number;
    presentDays: number;
    absentDays: number;
    lateDays: number;
  };
  upcomingTasks: Array<{
    id: string;
    title: string;
    description: string;
    dueDate: string;
    priority: string;
    status: string;
  }>;
  attendanceSummary: {
    isCheckedIn: boolean;
    checkInTime: string | null;
    checkOutTime: string | null;
    todayWorkHours: number;
    weekWorkHours: number;
    monthWorkHours: number;
    attendanceRate: number;
    presentDays: number;
    absentDays: number;
    lateDays: number;
  };
}

export interface SystemAnalytics {
  overview: {
    totalRevenue: number;
    revenueGrowth: number;
    totalUsers: number;
    userGrowth: number;
    totalOrganizations: number;
    systemUptime: number;
  };
  revenueByPlan: Array<{
    plan: string;
    revenue: number;
    percentage: number;
    organizations: number;
  }>;
  industryBreakdown: Array<{
    industry: string;
    organizations: number;
    users: number;
    revenue: number;
  }>;
  featureUsage: Array<{
    feature: string;
    usagePercentage: number;
    organizationsUsing: number;
  }>;
}

export interface SystemSettings {
  settings: {
    platformName: string;
    maintenanceMode: boolean;
    registrationEnabled: boolean;
    maxOrganizations: number;
    security: {
      passwordMinLength: number;
      sessionTimeout: number;
      twoFactorRequired: boolean;
    };
    notifications: {
      emailNotifications: boolean;
      systemAlerts: boolean;
    };
  };
}

export interface UpdateSystemSettingsRequest {
  settings: {
    maintenanceMode?: boolean;
    security?: {
      passwordMinLength?: number;
      sessionTimeout?: number;
      twoFactorRequired?: boolean;
    };
  };
}

class ApiService {
  private baseUrl: string;
  private token: string | null = null;
  private organizationId: string | null = null;

  constructor() {
    this.baseUrl = API_CONFIG.BASE_URL;
    // Try to get token from localStorage
    this.token = localStorage.getItem('authToken');
    // Try to get organization ID from localStorage
    this.organizationId = localStorage.getItem('organizationId');
  }

  // Set authentication token and organization ID
  setToken(token: string) {
    this.token = token;
    localStorage.setItem('authToken', token);
  }

  // Set organization ID
  setOrganizationId(organizationId: string) {
    this.organizationId = organizationId;
    localStorage.setItem('organizationId', organizationId);
  }

  // Clear authentication token and organization ID
  clearToken() {
    this.token = null;
    this.organizationId = null;
    localStorage.removeItem('authToken');
    localStorage.removeItem('organizationId');
  }

  // Get default headers
  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      ...API_CONFIG.HEADERS,
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    // Add organization ID header for tenant context
    if (this.organizationId) {
      headers['X-Organization-ID'] = this.organizationId;
    }

    return headers;
  }

  // Generic request method
  private async request<T>(
    endpoint: string,
    options: RequestInit & { params?: Record<string, string> } = {}
  ): Promise<ApiResponse<T>> {
    try {
      let url = buildApiUrl(endpoint);

      // Handle query parameters
      if (options.params) {
        const searchParams = new URLSearchParams(options.params);
        url += `?${searchParams.toString()}`;
      }

      // Remove params from options before passing to fetch
      const { params, ...fetchOptions } = options;

      const response = await fetch(url, {
        ...fetchOptions,
        headers: {
          ...this.getHeaders(),
          ...fetchOptions.headers,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        // For API errors, return the structured error response instead of throwing
        if (data && (data.error || data.message)) {
          return {
            success: false,
            data: null,
            error: data.error || { message: data.message },
            message: data.message
          };
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      return {
        success: false,
        data: null,
        error: {
          code: 'NETWORK_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error occurred',
        },
      };
    }
  }

  // GET request
  async get<T>(endpoint: string, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET', ...options });
  }

  // POST request
  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // PUT request
  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // Users API
  async getUsers(params: GetUsersParams = {}): Promise<ApiResponse<PaginatedResponse<UserDto>>> {
    const strParams: Record<string, string> = {};
    if (params.page) strParams.page = String(params.page);
    if (params.limit) strParams.limit = String(params.limit);
    if (params.department) strParams.department = params.department;
    if (params.role) strParams.role = params.role;
    if (params.search) strParams.search = params.search;
    return this.get<PaginatedResponse<UserDto>>(API_ENDPOINTS.USERS.LIST, { params: strParams });
  }

  async createEmployee(data: CreateEmployeeRequest): Promise<ApiResponse<UserDto>> {
    return this.post<UserDto>(API_ENDPOINTS.USERS.CREATE, data);
  }

  async updateUser(id: string, data: UpdateUserRequest): Promise<ApiResponse<UserDto>> {
    return this.put<UserDto>(API_ENDPOINTS.USERS.UPDATE(id), data);
  }

  async deleteUser(id: string): Promise<ApiResponse<any>> {
    return this.delete<any>(API_ENDPOINTS.USERS.DELETE(id));
  }

  // Employee Management API
  async getEmployees(params: GetUsersParams = {}): Promise<ApiResponse<PaginatedResponse<UserDto>>> {
    const strParams: Record<string, string> = {};
    if (params.page) strParams.page = String(params.page);
    if (params.limit) strParams.limit = String(params.limit);
    if (params.department) strParams.department = params.department;
    if (params.role) strParams.role = params.role;
    if (params.search) strParams.search = params.search;
    return this.get<PaginatedResponse<UserDto>>(API_ENDPOINTS.EMPLOYEE_MANAGEMENT.LIST, { params: strParams });
  }

  async getEmployeeById(id: string): Promise<ApiResponse<UserDto>> {
    return this.get<UserDto>(API_ENDPOINTS.EMPLOYEE_MANAGEMENT.GET(id));
  }

  async createEmployeeAdvanced(data: CreateEmployeeRequest): Promise<ApiResponse<UserDto>> {
    return this.post<UserDto>(API_ENDPOINTS.EMPLOYEE_MANAGEMENT.CREATE, data);
  }

  async updateEmployee(id: string, data: UpdateUserRequest): Promise<ApiResponse<UserDto>> {
    return this.put<UserDto>(API_ENDPOINTS.EMPLOYEE_MANAGEMENT.UPDATE(id), data);
  }

  async deleteEmployee(id: string): Promise<ApiResponse<any>> {
    return this.delete<any>(API_ENDPOINTS.EMPLOYEE_MANAGEMENT.DELETE(id));
  }

  async getOrganizationHierarchy(): Promise<ApiResponse<any>> {
    return this.get<any>(API_ENDPOINTS.EMPLOYEE_MANAGEMENT.HIERARCHY);
  }

  async validateHierarchyAssignment(employeeId: string, managerId: string): Promise<ApiResponse<boolean>> {
    return this.post<boolean>(API_ENDPOINTS.EMPLOYEE_MANAGEMENT.VALIDATE_HIERARCHY, {
      employeeId,
      managerId
    });
  }

  async getEmployeesByManager(managerId: string): Promise<ApiResponse<UserDto[]>> {
    return this.get<UserDto[]>(API_ENDPOINTS.EMPLOYEE_MANAGEMENT.BY_MANAGER(managerId));
  }

  async getEmployeesByDepartment(department: string): Promise<ApiResponse<UserDto[]>> {
    return this.get<UserDto[]>(API_ENDPOINTS.EMPLOYEE_MANAGEMENT.BY_DEPARTMENT(department));
  }

  async getEmployeeStats(): Promise<ApiResponse<any>> {
    return this.get<any>(API_ENDPOINTS.EMPLOYEE_MANAGEMENT.STATS);
  }

  async updateDatabaseSchema(): Promise<ApiResponse<any>> {
    return this.post<any>('/employee-management/update-schema', {});
  }

  // Generate credentials for existing employee
  async generateEmployeeCredentials(employeeId: string, requestBody: any = {}): Promise<ApiResponse<UserDto>> {
    return this.post<UserDto>(`/employee-management/employees/${employeeId}/generate-credentials`, requestBody);
  }

  // Change password for existing employee
  async changeEmployeePassword(employeeId: string, newPassword: string): Promise<ApiResponse<UserDto>> {
    return this.put<UserDto>(`/employee-management/employees/${employeeId}/change-password`, { newPassword });
  }

  // Employee Task Management
  async createSelfTask(data: {
    title: string;
    description?: string;
    priority?: string;
    category?: string;
    estimatedHours?: number;
    dueDate?: string;
    taskType?: string;
  }): Promise<ApiResponse<any>> {
    return this.post<any>(API_ENDPOINTS.EMPLOYEE_TASKS.CREATE_SELF, data);
  }

  async getEmployeeTasks(params: {
    status?: string;
    priority?: string;
    taskType?: string;
    isSelfCreated?: boolean;
    dueDateFrom?: string;
    dueDateTo?: string;
    page?: number;
    pageSize?: number;
  } = {}): Promise<ApiResponse<any>> {
    const strParams: Record<string, string> = {};
    if (params.status) strParams.status = params.status;
    if (params.priority) strParams.priority = params.priority;
    if (params.taskType) strParams.taskType = params.taskType;
    if (params.isSelfCreated !== undefined) strParams.isSelfCreated = String(params.isSelfCreated);
    if (params.dueDateFrom) strParams.dueDateFrom = params.dueDateFrom;
    if (params.dueDateTo) strParams.dueDateTo = params.dueDateTo;
    if (params.page) strParams.page = String(params.page);
    if (params.pageSize) strParams.pageSize = String(params.pageSize);

    return this.get<any>(API_ENDPOINTS.EMPLOYEE_TASKS.LIST, { params: strParams });
  }

  async createDailyUpdate(data: {
    taskId: string;
    updateNotes: string;
    progressPercentage?: number;
    hoursSpent?: number;
    blockers?: string;
    nextSteps?: string;
    updateDate?: string;
    updateType?: string;
  }): Promise<ApiResponse<any>> {
    return this.post<any>(API_ENDPOINTS.EMPLOYEE_TASKS.UPDATES, data);
  }

  async getEmployeeDailyUpdates(params: {
    taskId?: string;
    dateFrom?: string;
    dateTo?: string;
    updateType?: string;
    page?: number;
    pageSize?: number;
  } = {}): Promise<ApiResponse<any>> {
    const strParams: Record<string, string> = {};
    if (params.taskId) strParams.taskId = params.taskId;
    if (params.dateFrom) strParams.dateFrom = params.dateFrom;
    if (params.dateTo) strParams.dateTo = params.dateTo;
    if (params.updateType) strParams.updateType = params.updateType;
    if (params.page) strParams.page = String(params.page);
    if (params.pageSize) strParams.pageSize = String(params.pageSize);

    return this.get<any>(API_ENDPOINTS.EMPLOYEE_TASKS.UPDATES, { params: strParams });
  }

  async canViewTask(taskId: string): Promise<ApiResponse<boolean>> {
    return this.get<boolean>(API_ENDPOINTS.EMPLOYEE_TASKS.CAN_VIEW(taskId));
  }

  async canEditTask(taskId: string): Promise<ApiResponse<boolean>> {
    return this.get<boolean>(API_ENDPOINTS.EMPLOYEE_TASKS.CAN_EDIT(taskId));
  }

  // Manager Task Visibility
  async getManagerVisibleTasks(params: {
    employeeId?: string;
    status?: string;
    priority?: string;
    taskType?: string;
    isSelfCreated?: boolean;
    dueDateFrom?: string;
    dueDateTo?: string;
    page?: number;
    pageSize?: number;
  } = {}): Promise<ApiResponse<any>> {
    const strParams: Record<string, string> = {};
    if (params.employeeId) strParams.employeeId = params.employeeId;
    if (params.status) strParams.status = params.status;
    if (params.priority) strParams.priority = params.priority;
    if (params.taskType) strParams.taskType = params.taskType;
    if (params.isSelfCreated !== undefined) strParams.isSelfCreated = String(params.isSelfCreated);
    if (params.dueDateFrom) strParams.dueDateFrom = params.dueDateFrom;
    if (params.dueDateTo) strParams.dueDateTo = params.dueDateTo;
    if (params.page) strParams.page = String(params.page);
    if (params.pageSize) strParams.pageSize = String(params.pageSize);

    return this.get<any>(API_ENDPOINTS.MANAGER_TASKS.LIST, { params: strParams });
  }

  async getManagerVisibleUpdates(params: {
    employeeId?: string;
    taskId?: string;
    dateFrom?: string;
    dateTo?: string;
    updateType?: string;
    page?: number;
    pageSize?: number;
  } = {}): Promise<ApiResponse<any>> {
    const strParams: Record<string, string> = {};
    if (params.employeeId) strParams.employeeId = params.employeeId;
    if (params.taskId) strParams.taskId = params.taskId;
    if (params.dateFrom) strParams.dateFrom = params.dateFrom;
    if (params.dateTo) strParams.dateTo = params.dateTo;
    if (params.updateType) strParams.updateType = params.updateType;
    if (params.page) strParams.page = String(params.page);
    if (params.pageSize) strParams.pageSize = String(params.pageSize);

    return this.get<any>(API_ENDPOINTS.MANAGER_TASKS.UPDATES, { params: strParams });
  }

  async getEmployeeTasksForManager(employeeId: string, params: {
    status?: string;
    priority?: string;
    taskType?: string;
    isSelfCreated?: boolean;
    page?: number;
    pageSize?: number;
  } = {}): Promise<ApiResponse<any>> {
    const strParams: Record<string, string> = {};
    if (params.status) strParams.status = params.status;
    if (params.priority) strParams.priority = params.priority;
    if (params.taskType) strParams.taskType = params.taskType;
    if (params.isSelfCreated !== undefined) strParams.isSelfCreated = String(params.isSelfCreated);
    if (params.page) strParams.page = String(params.page);
    if (params.pageSize) strParams.pageSize = String(params.pageSize);

    return this.get<any>(API_ENDPOINTS.MANAGER_TASKS.EMPLOYEE_TASKS(employeeId), { params: strParams });
  }

  async getEmployeeUpdatesForManager(employeeId: string, params: {
    taskId?: string;
    dateFrom?: string;
    dateTo?: string;
    updateType?: string;
    page?: number;
    pageSize?: number;
  } = {}): Promise<ApiResponse<any>> {
    const strParams: Record<string, string> = {};
    if (params.taskId) strParams.taskId = params.taskId;
    if (params.dateFrom) strParams.dateFrom = params.dateFrom;
    if (params.dateTo) strParams.dateTo = params.dateTo;
    if (params.updateType) strParams.updateType = params.updateType;
    if (params.page) strParams.page = String(params.page);
    if (params.pageSize) strParams.pageSize = String(params.pageSize);

    return this.get<any>(API_ENDPOINTS.MANAGER_TASKS.EMPLOYEE_UPDATES(employeeId), { params: strParams });
  }

  // Legacy task method for backward compatibility
  async getTasks(): Promise<ApiResponse<any[]>> {
    const result = await this.getEmployeeTasks();
    return {
      success: result.success,
      data: result.data?.tasks || [],
      error: result.error
    };
  }

  // Attendance API
  async checkIn(timestamp: string, location?: string, notes?: string): Promise<ApiResponse<any>> {
    return this.post<any>(API_ENDPOINTS.ATTENDANCE.CHECK_IN, { timestamp, location, notes });
  }

  async checkOut(timestamp: string, location?: string, notes?: string): Promise<ApiResponse<any>> {
    return this.post<any>(API_ENDPOINTS.ATTENDANCE.CHECK_OUT, { timestamp, location, notes });
  }

  async getAttendanceRecords(params: { userId?: string; startDate?: string; endDate?: string } = {}): Promise<ApiResponse<any>> {
    const strParams: Record<string, string> = {};
    if (params.userId) strParams.userId = params.userId;
    if (params.startDate) strParams.startDate = params.startDate;
    if (params.endDate) strParams.endDate = params.endDate;
    return this.get<any>(API_ENDPOINTS.ATTENDANCE.RECORDS, { params: strParams });
  }

  async getOrganizationAttendance(params: {
    date?: string;
    startDate?: string;
    endDate?: string;
    department?: string;
    status?: string
  } = {}): Promise<ApiResponse<any>> {
    const strParams: Record<string, string> = {};
    if (params.date) strParams.date = params.date;
    if (params.startDate) strParams.startDate = params.startDate;
    if (params.endDate) strParams.endDate = params.endDate;
    if (params.department) strParams.department = params.department;
    if (params.status) strParams.status = params.status;
    return this.get<any>(API_ENDPOINTS.ATTENDANCE.ORGANIZATION, { params: strParams });
  }

  // Leave API
  async getLeaveBalance(params: { userId?: string; year?: number } = {}): Promise<ApiResponse<any>> {
    const strParams: Record<string, string> = {};
    if (params.userId) strParams.userId = params.userId;
    if (params.year) strParams.year = String(params.year);
    return this.get<any>(API_ENDPOINTS.LEAVE.BALANCE, { params: strParams });
  }

  async applyLeave(data: any): Promise<ApiResponse<any>> {
    return this.post<any>(API_ENDPOINTS.LEAVE.APPLY, data);
  }


  async getLeaveTypes(): Promise<ApiResponse<any>> {
    return this.get<any>(API_ENDPOINTS.LEAVE.TYPES);
  }

  async getLeaveRequests(params: { userId?: string; status?: string } = {}): Promise<ApiResponse<any>> {
    const strParams: Record<string, string> = {};
    if (params.userId) strParams.userId = params.userId;
    if (params.status) strParams.status = params.status;
    return this.get<any>(API_ENDPOINTS.LEAVE.REQUESTS, { params: strParams });
  }

  async updateLeaveStatus(id: string, data: { status: string; comment?: string }): Promise<ApiResponse<any>> {
    return this.put<any>(API_ENDPOINTS.LEAVE.UPDATE_STATUS(id), data);
  }

  // Organization Admin Leave Management API
  async getOrganizationLeaveRequests(params: {
    status?: string;
    startDate?: string;
    endDate?: string;
    department?: string;
    employeeId?: string;
    page?: number;
    pageSize?: number;
  } = {}): Promise<ApiResponse<any>> {
    const strParams: Record<string, string> = {};
    if (params.status) strParams.status = params.status;
    if (params.startDate) strParams.startDate = params.startDate;
    if (params.endDate) strParams.endDate = params.endDate;
    if (params.department) strParams.department = params.department;
    if (params.employeeId) strParams.employeeId = params.employeeId;
    if (params.page) strParams.page = String(params.page);
    if (params.pageSize) strParams.pageSize = String(params.pageSize);

    return this.get<any>(API_ENDPOINTS.ORG_ADMIN.LEAVE_REQUESTS, { params: strParams });
  }

  async getLeaveStatistics(params: {
    startDate?: string;
    endDate?: string;
    department?: string;
  } = {}): Promise<ApiResponse<any>> {
    const strParams: Record<string, string> = {};
    if (params.startDate) strParams.startDate = params.startDate;
    if (params.endDate) strParams.endDate = params.endDate;
    if (params.department) strParams.department = params.department;

    return this.get<any>(API_ENDPOINTS.ORG_ADMIN.LEAVE_STATISTICS, { params: strParams });
  }

  async approveRejectLeave(id: string, data: { status: string; comments?: string }): Promise<ApiResponse<any>> {
    return this.put<any>(API_ENDPOINTS.ORG_ADMIN.APPROVE_REJECT_LEAVE(id), data);
  }

  // Organization Admin Task Management API
  async getOrganizationTasks(params: {
    status?: string;
    priority?: string;
    category?: string;
    department?: string;
    assignedTo?: string;
    assignedBy?: string;
    dueDateFrom?: string;
    dueDateTo?: string;
    overdue?: boolean;
    page?: number;
    pageSize?: number;
  } = {}): Promise<ApiResponse<any>> {
    const strParams: Record<string, string> = {};
    if (params.status) strParams.status = params.status;
    if (params.priority) strParams.priority = params.priority;
    if (params.category) strParams.category = params.category;
    if (params.department) strParams.department = params.department;
    if (params.assignedTo) strParams.assignedTo = params.assignedTo;
    if (params.assignedBy) strParams.assignedBy = params.assignedBy;
    if (params.dueDateFrom) strParams.dueDateFrom = params.dueDateFrom;
    if (params.dueDateTo) strParams.dueDateTo = params.dueDateTo;
    if (params.overdue !== undefined) strParams.overdue = String(params.overdue);
    if (params.page) strParams.page = String(params.page);
    if (params.pageSize) strParams.pageSize = String(params.pageSize);

    return this.get<any>(API_ENDPOINTS.ORG_ADMIN.TASKS, { params: strParams });
  }

  async getTaskStatistics(params: {
    startDate?: string;
    endDate?: string;
    department?: string;
    category?: string;
  } = {}): Promise<ApiResponse<any>> {
    const strParams: Record<string, string> = {};
    if (params.startDate) strParams.startDate = params.startDate;
    if (params.endDate) strParams.endDate = params.endDate;
    if (params.department) strParams.department = params.department;
    if (params.category) strParams.category = params.category;

    return this.get<any>(API_ENDPOINTS.ORG_ADMIN.TASK_STATISTICS, { params: strParams });
  }

  async assignTask(data: {
    title: string;
    description?: string;
    assignedTo: string;
    priority?: string;
    category?: string;
    estimatedHours?: number;
    dueDate?: string;
  }): Promise<ApiResponse<any>> {
    return this.post<any>(API_ENDPOINTS.ORG_ADMIN.ASSIGN_TASK, data);
  }

  async getTaskUpdates(params: {
    startDate?: string;
    endDate?: string;
    employeeId?: string;
    department?: string;
    page?: number;
    pageSize?: number;
  } = {}): Promise<ApiResponse<any>> {
    const strParams: Record<string, string> = {};
    if (params.startDate) strParams.startDate = params.startDate;
    if (params.endDate) strParams.endDate = params.endDate;
    if (params.employeeId) strParams.employeeId = params.employeeId;
    if (params.department) strParams.department = params.department;
    if (params.page) strParams.page = String(params.page);
    if (params.pageSize) strParams.pageSize = String(params.pageSize);

    return this.get<any>(API_ENDPOINTS.ORG_ADMIN.TASK_UPDATES, { params: strParams });
  }

  async updateTaskStatus(id: string, data: { status: string; comments?: string; progressPercentage?: number }): Promise<ApiResponse<any>> {
    return this.put<any>(API_ENDPOINTS.ORG_ADMIN.UPDATE_TASK_STATUS(id), data);
  }

  // Payroll API
  async getEmployeePayroll(params: { month?: number; year?: number } = {}): Promise<ApiResponse<any>> {
    const strParams: Record<string, string> = {};
    if (params.month) strParams.month = String(params.month);
    if (params.year) strParams.year = String(params.year);
    return this.get<any>(API_ENDPOINTS.PAYROLL.EMPLOYEE, { params: strParams });
  }

  async getEmployeeBenefits(): Promise<ApiResponse<any>> {
    return this.get<any>(API_ENDPOINTS.PAYROLL.BENEFITS);
  }

  // Performance API
  async getPerformanceReviews(params: { employeeId?: string; reviewerId?: string; status?: string } = {}): Promise<ApiResponse<any>> {
    const strParams: Record<string, string> = {};
    if (params.employeeId) strParams.employeeId = params.employeeId;
    if (params.reviewerId) strParams.reviewerId = params.reviewerId;
    if (params.status) strParams.status = params.status;
    return this.get<any>(API_ENDPOINTS.PERFORMANCE.REVIEWS, { params: strParams });
  }

  async createPerformanceReview(data: any): Promise<ApiResponse<any>> {
    return this.post<any>(API_ENDPOINTS.PERFORMANCE.CREATE_REVIEW, data);
  }



  // DELETE request
  async delete<T>(endpoint: string, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE', ...options });
  }

  // Authentication methods
  async login(credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    const response = await this.post<LoginResponse>(API_ENDPOINTS.AUTH.LOGIN, credentials);

    if (response.success && response.data?.token) {
      this.setToken(response.data.token);

      // Set organization ID if available
      if (response.data.user.organization?.id) {
        this.setOrganizationId(response.data.user.organization.id);
      }
    }

    return response;
  }



  async logout(): Promise<ApiResponse<void>> {
    const response = await this.post<void>(API_ENDPOINTS.AUTH.LOGOUT);
    this.clearToken();
    return response;
  }

  async getCurrentUser(): Promise<ApiResponse<LoginResponse['user']>> {
    return this.get<LoginResponse['user']>(API_ENDPOINTS.USERS.ME);
  }

  // Health check
  async healthCheck(): Promise<ApiResponse<any>> {
    return this.get(API_ENDPOINTS.HEALTH.CHECK);
  }

  // Dashboard APIs

  async getOrganizationAdminDashboard(): Promise<ApiResponse<OrganizationAdminDashboard>> {
    return this.get<OrganizationAdminDashboard>(API_ENDPOINTS.DASHBOARD.ORG_ADMIN);
  }

  async getEmployeeDashboard(): Promise<ApiResponse<EmployeeDashboard>> {
    return this.get<EmployeeDashboard>(API_ENDPOINTS.DASHBOARD.EMPLOYEE);
  }

  async getDashboardStats(): Promise<ApiResponse<any>> {
    return this.get<any>(API_ENDPOINTS.DASHBOARD.STATS);
  }

  // Admin Employee Dashboard Access
  async getEmployeeDashboardForAdmin(employeeId: string): Promise<ApiResponse<EmployeeDashboard>> {
    return this.get<EmployeeDashboard>(API_ENDPOINTS.ORG_ADMIN.EMPLOYEE_DASHBOARD(employeeId));
  }

  async getEmployeeProfileForAdmin(employeeId: string): Promise<ApiResponse<any>> {
    return this.get<any>(API_ENDPOINTS.ORG_ADMIN.EMPLOYEE_PROFILE(employeeId));
  }

  async getEmployeeAttendanceForAdmin(employeeId: string): Promise<ApiResponse<any>> {
    return this.get<any>(API_ENDPOINTS.ORG_ADMIN.EMPLOYEE_ATTENDANCE(employeeId));
  }

  // Super Admin APIs
  async getSuperAdminDashboard(): Promise<ApiResponse<SuperAdminDashboard>> {
    return this.get<SuperAdminDashboard>(API_ENDPOINTS.DASHBOARD.SUPER_ADMIN);
  }

  async getOrganizations(params: {
    page?: number;
    pageSize?: number;
    search?: string;
    status?: string;
    industry?: string;
  } = {}): Promise<ApiResponse<any>> {
    const strParams: Record<string, string> = {};
    if (params.page) strParams.page = params.page.toString();
    if (params.pageSize) strParams.pageSize = params.pageSize.toString();
    if (params.search) strParams.search = params.search;
    if (params.status) strParams.status = params.status;
    if (params.industry) strParams.industry = params.industry;

    return this.get<any>(API_ENDPOINTS.SUPER_ADMIN.ORGANIZATIONS, { params: strParams });
  }

  async getOrganizationDetails(id: string): Promise<ApiResponse<any>> {
    return this.get<any>(API_ENDPOINTS.SUPER_ADMIN.ORGANIZATION_DETAILS(id));
  }

  async createOrganization(data: {
    name: string;
    domain: string;
    industry: string;
    adminName: string;
    adminEmail: string;
  }): Promise<ApiResponse<any>> {
    return this.post<any>(API_ENDPOINTS.SUPER_ADMIN.CREATE_ORGANIZATION, data);
  }

  async updateOrganization(id: string, data: any): Promise<ApiResponse<any>> {
    return this.put<any>(API_ENDPOINTS.SUPER_ADMIN.UPDATE_ORGANIZATION(id), data);
  }

  async getOrganizationAdminCredentials(id: string): Promise<ApiResponse<any>> {
    return this.get<any>(API_ENDPOINTS.SUPER_ADMIN.ORGANIZATION_ADMIN_CREDENTIALS(id));
  }

  async resetOrganizationAdminPassword(id: string): Promise<ApiResponse<any>> {
    return this.post<any>(API_ENDPOINTS.SUPER_ADMIN.RESET_ORGANIZATION_ADMIN_PASSWORD(id), {});
  }

  async updateOrganizationStatus(id: string, data: { status: string; reason?: string }): Promise<ApiResponse<any>> {
    return this.put<any>(API_ENDPOINTS.SUPER_ADMIN.UPDATE_ORGANIZATION_STATUS(id), data);
  }

  async deleteOrganization(id: string, force: boolean = false): Promise<ApiResponse<any>> {
    return this.delete<any>(`${API_ENDPOINTS.SUPER_ADMIN.DELETE_ORGANIZATION(id)}?force=${force}`);
  }

  async deleteOrganizationByName(name: string, force: boolean = false): Promise<ApiResponse<any>> {
    const url = `${API_ENDPOINTS.SUPER_ADMIN.DELETE_ORGANIZATION_BY_NAME(name)}${force ? '?force=true' : ''}`;
    return this.delete<any>(url);
  }

  async getSystemAnalytics(params: {
    period?: string;
    startDate?: string;
    endDate?: string;
  } = {}): Promise<ApiResponse<any>> {
    const strParams: Record<string, string> = {};
    if (params.period) strParams.period = params.period;
    if (params.startDate) strParams.startDate = params.startDate;
    if (params.endDate) strParams.endDate = params.endDate;

    return this.get<any>(API_ENDPOINTS.SUPER_ADMIN.ANALYTICS, { params: strParams });
  }

  async getSystemSettings(): Promise<ApiResponse<SystemSettings>> {
    return this.get<SystemSettings>(API_ENDPOINTS.SUPER_ADMIN.SETTINGS);
  }

  async updateSystemSettings(data: UpdateSystemSettingsRequest): Promise<ApiResponse<SystemSettings>> {
    return this.put<SystemSettings>(API_ENDPOINTS.SUPER_ADMIN.SETTINGS, data);
  }

}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
